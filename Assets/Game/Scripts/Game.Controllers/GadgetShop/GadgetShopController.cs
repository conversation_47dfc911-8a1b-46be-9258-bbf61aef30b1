using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.GadgetShop;
using Game.Views.Levels;
using Game.Views.Players;
using Modules.Core;
using VContainer;

namespace Game.Controllers.GadgetShop
{
    public class GadgetShopController : ControllerBase
    {
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private EconomyModel economyModel;
        private GadgetShopManager gadgetShopManager;
        private LevelSpaceManager levelSpaceManager;
        private InteractablesModel interactablesModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            PlayersModel playersModel,
            EconomyModel economyModel,
            LevelSpaceManager levelSpaceManager,
            GadgetShopManager gadgetShopManager,
            InteractablesModel interactablesModel)
        {
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.economyModel = economyModel;
            this.gadgetShopManager = gadgetShopManager;
            this.levelSpaceManager = levelSpaceManager;
            this.interactablesModel = interactablesModel;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            gadgetShopManager.OnClicked.Subscribe(HandleClicked).AddTo(player);
        }

        private void HandleLevelLoaded()
        {
            gadgetShopManager.ClearInstances();

            if (levelModel.IsMinesLevel)
            {
                levelSpaceManager.GadgetShopViewNodeList.ForEach(n => gadgetShopManager.CreateInstance(n.GetPose()));
            }
        }

        private void HandleClicked(GadgetInventoryView gadgetInventoryView)
        {
            ResolveInteractable(gadgetInventoryView).Forget();
        }

        private async UniTaskVoid ResolveInteractable(GadgetInventoryView gadgetInventoryView)
        {
            var data = gadgetInventoryView.Data;
            var interactor = gadgetInventoryView.GrabInteractor;

            if (economyModel.DiamondAmount.Value >= data.diamondPrice)
            {
                var result = await economyModel.AddDiamondAmount(-data.diamondPrice, DisposeCancellationToken);
                if (result.IsOk && interactor != null && LocalPlayer != null)
                {
                    interactablesModel.CreateInteractable(data.viewCode, interactor, true);
                }
            }
            // Show Popup
        }
    }
}