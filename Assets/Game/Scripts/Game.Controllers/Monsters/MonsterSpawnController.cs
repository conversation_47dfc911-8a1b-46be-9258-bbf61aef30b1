using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.Voxels;
using Game.Views.World;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public class MonsterSpawnController : ControllerBase
    {
        private LevelModel levelModel;
        private VoxelSpaceManager voxelSpaceManager;
        private WorldBoundary worldBoundary;
        private MonstersManager monstersManager;
        private INetworkClient networkClient;
        private MonstersConfig monstersConfig;

        private bool UseMonster => levelModel.LevelConfig.UseMonsters;
        private bool IsVoxelWorldLoaded => voxelSpaceManager.IsMapLoaded.Value;
        private bool IsMasterClient => networkClient.IsMasterClient.Value;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            VoxelSpaceManager voxelSpaceManager,
            WorldBoundary worldBoundary,
            MonstersManager monstersManager,
            INetworkClient networkClient,
            MonstersConfig monstersConfig)
        {
            this.levelModel = levelModel;
            this.voxelSpaceManager = voxelSpaceManager;
            this.networkClient = networkClient;
            this.monstersManager = monstersManager;
            this.worldBoundary = worldBoundary;
            this.monstersConfig = monstersConfig;

            voxelSpaceManager.IsMapLoaded.Where(ok => ok).Subscribe(_ => TrySpawnMonsters()).AddTo(DisposeCancellationToken);
            networkClient.IsConnected.Where(ok => ok).Subscribe(_ => TrySpawnMonsters()).AddTo(DisposeCancellationToken);
            networkClient.OnNetworkActorDespawned.Subscribe(x => HandleNetworkActorDespawned(x.actor)).AddTo(DisposeCancellationToken);
        }

        private void HandleNetworkActorDespawned(NetworkActor actor)
        {
            if (actor is not MonsterActor monsterActor)
            {
                return;
            }

            var monsterIndex = monsterActor.MonsterDataIndex;
            var monsterData = levelModel.Level.monsters?[monsterIndex];
            var delay = TimeSpan.FromSeconds(10f);
            if (monstersConfig.TryGetMonsterData((byte)monsterIndex, out var monsterConfigData))
            {
                if (monsterConfigData.RespawnDelay > 0f)
                {
                    delay = TimeSpan.FromSeconds(monsterConfigData.RespawnDelay);
                }
            }
            var token0 = networkClient.DisconnectionCancellationToken;
            var token1 = DisposeCancellationToken;
            var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(token0, token1).Token;

            UniTaskAsyncEnumerable.Timer(delay).Subscribe(_ =>
            {
                if (!networkClient.IsMasterClient.Value)
                {
                    return;
                }

                if (monsterData == null)
                {
                    return;
                }

                monstersManager.CreateMonster(monsterIndex, monsterData.spawn, GetRotation(monsterData.rotation), monsterData.code);
            }).AddTo(linkedToken);
        }

        private void TrySpawnMonsters()
        {
            if (!UseMonster || !IsMasterClient || !IsVoxelWorldLoaded)
            {
                return;
            }

            SpawnMonsters();
        }

        private void SpawnMonsters()
        {
            if (levelModel.Level.monsters == null)
            {
                return;
            }

            for (var i = 0; i < levelModel.Level.monsters.Count; i++)
            {
                var spawnData = levelModel.Level.monsters[i];
                monstersManager.CreateMonster(i, spawnData.spawn, GetRotation(spawnData.rotation), spawnData.code);
            }
        }

        private bool TryGetRandomPoint(Vector3 origin, float radius, out Vector3 point)
        {
            point = origin + Random.insideUnitSphere.SetY(0) * radius;
            point.y = voxelSpaceManager.GetTerrainHeight(point);

            return point.y > 1 && !voxelSpaceManager.IsWaterVoxel(point) && worldBoundary.Contains(point);
        }

        private static Quaternion GetRandomRotation()
        {
            return Quaternion.AngleAxis(Random.Range(-180, 180), Vector3.up);
        }

        private static Quaternion GetRotation(float angle)
        {
            return Quaternion.AngleAxis(angle, Vector3.up);
        }
    }
}