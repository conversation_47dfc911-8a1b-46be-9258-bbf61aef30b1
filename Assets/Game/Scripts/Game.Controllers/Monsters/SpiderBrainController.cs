using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.Players;
using Game.Views.Voxels;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Monsters
{
    public class SpiderBrainController : NetworkActor
    {
        [SerializeField] private MonsterActor monster;
        [SerializeField] private Rigidbody rootRigidbody;

        private Vector3 Forward => MonsterTransform.forward;
        private Vector3 Up => MonsterTransform.up;
        private Vector3 Down => -Up;
        private Transform MonsterTransform => monster.transform;

        private LevelModel levelModel;
        private MonstersConfig monstersConfig;
        private PlayersModel playersModel;
        private IAudioClient audioClient;
        private VoxelSpaceManager voxelSpaceManager;

        private float nextDamageReceiveTime;
        private float nextVoiceTime;
        private float nextAttackTime;
        private int targetPlayerId;

        private Vector3 patrolDirection;
        private float nextPathUpdateTime;
        private float nextPlayerCheckTime;
        private float nextTargetScanTime;
        private float nextPatrolDirectionTime;
        private Vector3 chaseDirection;
        private float nextRotationTime;
        private float nextIdleOrWalkSwitchTime;
        private float nextJumpTime;
        private bool spawned;
        
        private bool HasTarget => targetPlayer != null;
        private bool IsAttacking => Time.time < nextAttackTime;
        private bool IsDamageReceiving => Time.time < nextDamageReceiveTime;
        private bool IsAlive => monster.IsAlive;
        private float ChaseSpeed => monster.ChaseSpeed;
        private byte AttackDamage => monster.AttackDamage;
        private float PatrolSpeed => monster.PatrolSpeed;
        private bool CanRandomRotate => Time.time > nextRotationTime;
        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private PlayerActor targetPlayer;

        private MonsterState State
        {
            get => monster.State;
            set
            {
                previousFrameState = monster.State;
                monster.State = value;
            }
        }

        private MonsterState previousFrameState;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            MonstersConfig monstersConfig,
            PlayersModel playersModel,
            ISubscriber<MonsterDamageArgs> damageSubscriber,
            IAudioClient audioClient,
            VoxelSpaceManager voxelSpaceManager)
        {
            this.levelModel = levelModel;
            this.monstersConfig = monstersConfig;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            this.voxelSpaceManager = voxelSpaceManager;

            damageSubscriber.Subscribe(HandleDamage).AddTo(destroyCancellationToken);
        }

        public override void Render()
        {
            base.Render();
            if (IsAlive)
            {
                PlayRandomVoice();
            }

            if (!Runner.IsSharedModeMasterClient)
            {
                return;
            }
            
            if (monster.StateAuthority == PlayerRef.Invalid || monster.StateAuthority == PlayerRef.None && Time.time >= nextPlayerCheckTime)
            {
                nextPlayerCheckTime = Time.time + 2f;
                Debug.LogWarning("[SpiderBrainController]: Object lost State Authority! Requesting transfer to master client...");
                TransferAuthority().Forget();
            }
        }
        
        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            CheckToReleasePlayerTarget();
            ScanTarget();
            UpdateStates();
        }

        private void ScanTarget()
        {
            if (targetPlayer != null) return;
            if (playersModel.TryFindClosestPlayer(monster.transform.position, Mathf.FloorToInt(monstersConfig.SpiderAttackMinDistance), IsPlayerTargetable, out var player, 3))
            {
                SetNewTargetRpc(player);
            }
        }

        public override void Spawned()
        {
            base.Spawned();
            if (HasStateAuthority)
            {
                if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
                {
                    var monsterData = levelModel.Level.monsters[monster.MonsterDataIndex];
                    MonsterTransform.position = monsterData.spawn;
                }
            }

            spawned = true;
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            spawned = false;
        }

        private void UpdateStates()
        {
            if (!HasTarget)
            {
                TryApplyGravity();
                if (Time.time >= nextIdleOrWalkSwitchTime)
                {
                    nextIdleOrWalkSwitchTime = Time.time + Random.Range(1f, 15f);
                    State = State == MonsterState.Idle ? MonsterState.Patrol : MonsterState.Idle;
                }

                if (State == MonsterState.Patrol)
                {
                    Patrol();
                }
            }
            else
            {
                if (IsInRange())
                {
                    State = MonsterState.Attack;
                }
                else
                {
                    State = MonsterState.Chase;
                }

                nextIdleOrWalkSwitchTime = Time.time;
            }

            HandleTransitions();
            if (State == MonsterState.Attack)
            {
                Attack();
            }

            if (State == MonsterState.Chase)
            {
                Chase();
            }
        }

        private void Chase()
        {
            if (Time.time > nextJumpTime && rootRigidbody.velocity.magnitude < 0.1f)
            {
                ApplyJumpForce(null);
            }
        }

        private void RotateToUpright()
        {
            var targetRotation = Quaternion.FromToRotation(MonsterTransform.up, Vector3.up) * MonsterTransform.rotation;
            MonsterTransform.rotation = targetRotation;
        }

        private void ApplyJumpForce(Vector3? collisionNormal)
        {
            if (!HasTarget) return;

            var directionToTarget = (targetPlayer.transform.position - MonsterTransform.position).normalized;
            var jumpForce = ChaseSpeed;
            var jumpDirection = Vector3.zero;

            var randomX = Random.Range(-0.2f, 0.2f);
            var randomY = Random.Range(2f, 5f);
            var randomZ = Random.Range(-0.2f, 0.2f);
            var randomDirection = new Vector3(randomX, randomY, randomZ);

            if (collisionNormal.HasValue)
            {
                var normal = collisionNormal.Value;

                if (normal.y < -0.7f)
                {
                    // Hit roof - jump straight to player
                    jumpDirection = directionToTarget.normalized;
                }
                else if (Mathf.Abs(normal.y) < 0.3f)
                {
                    // Hit wall - check if there's obstacle between spider and player
                    if (IsObstacleBetweenSpiderAndPlayer())
                    {
                        // Obstacle detected - jump straight up
                        jumpDirection = Vector3.up;
                        jumpForce = ChaseSpeed * 1.2f;
                    }
                    else
                    {
                        // No obstacle - jump straight to player
                        jumpDirection = directionToTarget.normalized;
                    }
                }
                else
                {
                    // Hit ground - jump towards player with up component
                    jumpDirection = (directionToTarget + Vector3.up * 2f + randomDirection).normalized;
                }
            }
            else
            {
                jumpDirection = (directionToTarget + randomDirection).normalized;
            }

            RotateToJumpDirection(jumpDirection);

            var currentVelocity = rootRigidbody.velocity;
            var currentHorizontalVelocity = new Vector3(currentVelocity.x, 0, currentVelocity.z);
            var newHorizontalDirection = new Vector3(jumpDirection.x, 0, jumpDirection.z).normalized;

            if (currentHorizontalVelocity.magnitude > 0.1f)
            {
                var currentHorizontalDirection = currentHorizontalVelocity.normalized;
                var angle = Vector3.Angle(currentHorizontalDirection, newHorizontalDirection);

                if (angle > 45f)
                {
                    rootRigidbody.velocity = Vector3.zero;
                }
            }
            else
            {
                rootRigidbody.velocity = Vector3.zero;
            }

            nextJumpTime = Time.time + 1f;
            rootRigidbody.AddForce(jumpDirection * jumpForce, ForceMode.Acceleration);
        }

        private bool IsObstacleBetweenSpiderAndPlayer()
        {
            if (!HasTarget) return false;

            var spiderPosition = MonsterTransform.position;
            var playerPosition = targetPlayer.transform.position;
            var directionToPlayer = (playerPosition - spiderPosition).normalized;
            var distanceToPlayer = Vector3.Distance(spiderPosition, playerPosition);

            // Raycast from spider to player to check for obstacles
            return Physics.Raycast(spiderPosition, directionToPlayer, distanceToPlayer - 0.5f, 1 << Layers.Default);
        }

        private void RotateToJumpDirection(Vector3 jumpDirection)
        {
            var horizontalDirection = new Vector3(jumpDirection.x, 0, jumpDirection.z).normalized;

            if (horizontalDirection.sqrMagnitude > 0.1f)
            {
                var targetRotation = Quaternion.LookRotation(horizontalDirection, Vector3.up);
                MonsterTransform.rotation = targetRotation;
            }
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (!spawned) return;
            if (collision.gameObject.layer != Layers.Default)
            {
                return;
            }

            if (State == MonsterState.Patrol || State == MonsterState.Idle)
            {
                return;
            }

            var normal = collision.contacts[0].normal;
            ApplyJumpForce(normal);
        }

        private void Patrol()
        {
            if (CanRandomRotate)
            {
                SetRandomRotation();
                nextRotationTime = Time.time + Random.Range(1f, 10f);
            }

            UpdateOrientation();
            MoveForward(PatrolSpeed);
        }

        private void SetRandomRotation()
        {
            MonsterTransform.rotation *= Quaternion.AngleAxis(Random.Range(-180f, 180f), Vector3.up);
        }

        private void UpdateOrientation()
        {
            if (UsingPhysics())
            {
                return;
            }

            if (TryDetectOrientation(out var point, out var normal))
            {
                SetOrientation(point, normal);
            }
        }

        private bool TryDetectOrientation(out Vector3 point, out Vector3 normal)
        {
            const float forwardOffset = 0.5f;
            const float downOffset = 0.25f;
            const float upOffset = 0.5f;
            const float backOffset = 10f;

            if (TryRaycast(MonsterTransform.position + upOffset * Up, Forward, forwardOffset, out var hitWall))
            {
                point = hitWall.point;
                normal = hitWall.normal;
                return true;
            }

            if (!voxelSpaceManager.CheckCollision(MonsterTransform.position + downOffset * Forward + downOffset * Down))
            {
                if (TryRaycast(MonsterTransform.position + forwardOffset * Forward + downOffset * Down, -Forward, backOffset, out var hit))
                {
                    point = hit.point;
                    normal = hit.normal;
                    return true;
                }
            }

            point = normal = Vector3.zero;
            return false;
        }

        private void SetOrientation(Vector3 point, Vector3 normal)
        {
            var localRotation = Quaternion.Inverse(MonsterTransform.rotation);
            var angle = -localRotation.eulerAngles.y;

            MonsterTransform.position = point;
            MonsterTransform.up = normal;
            MonsterTransform.rotation *= Quaternion.AngleAxis(angle, Vector3.up);
        }

        private bool TryApplyGravity()
        {
            if (UsingPhysics()) return false;
            if (TryRaycast(MonsterTransform.position + 0.2f * Up, Down, 50, out var hit) && hit.distance > 0.5f)
            {
                monster.Teleport(hit.point);
                return true;
            }

            return false;
        }

        private void HandleTransitions()
        {
            if (previousFrameState == MonsterState.Attack && State != MonsterState.Attack)
            {
                nextAttackTime = 0;
            }

            if (previousFrameState != MonsterState.Attack && State == MonsterState.Attack)
            {
                nextAttackTime = 0;
                SetAttackAudioRpc();
            }

            if (State == MonsterState.Patrol || State == MonsterState.Idle)
            {
                DisablePhysics();
            }
            else
            {
                EnablePhysics();
            }

            if (previousFrameState != MonsterState.Chase && State == MonsterState.Chase)
                SetScreamRpc(true);

            if (previousFrameState == MonsterState.Chase && State != MonsterState.Chase)
                SetScreamRpc(false);
        }

        private bool IsInRange()
        {
            return Vector3.Distance(targetPlayer.transform.position, MonsterTransform.position) < monstersConfig.SpiderAttackMinDistance;
        }

        private void JumpToPlayerFace()
        {
            var targetPosition = targetPlayer.HeadNode.position + targetPlayer.HeadNode.transform.forward * 0.75f + targetPlayer.HeadNode.transform.up * -0.2f;
            MonsterTransform.position = targetPosition;
            MonsterTransform.rotation = Quaternion.LookRotation(-targetPlayer.transform.forward.OnlyXZ().normalized, Vector3.up);
        }

        private void DisablePhysics()
        {
            rootRigidbody.constraints = RigidbodyConstraints.FreezeAll;
            rootRigidbody.useGravity = false;
            rootRigidbody.isKinematic = true;
        }

        private void EnablePhysics()
        {
            RotateToUpright();
            rootRigidbody.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
            rootRigidbody.useGravity = true;
            rootRigidbody.isKinematic = false;
        }

        private void CheckToReleasePlayerTarget()
        {
            if (targetPlayer == null) return;

            if (!IsPlayerTargetable(targetPlayer))
            {
                SetNewTargetRpc(null);
                return;
            }

            if (Vector3.Distance(targetPlayer.transform.position, MonsterTransform.position) > monstersConfig.SpiderScanPlayerRadius)
            {
                SetNewTargetRpc(null);
            }
        }

        private async UniTaskVoid TransferAuthority()
        {
            await Object.RequestStateAuthorityAsync(DespawnCancellationToken);
            nextJumpTime = Time.time + 1f;
        }

        private bool IsPlayerTargetable(PlayerActor player)
        {
            if (levelModel.LevelConfig.IsInfectedWhenDead)
                return player.IsAlive && !player.IsTagged.Value;

            return player.IsAlive;
        }

        private void Attack()
        {
            if (nextAttackTime == 0)
            {
                nextAttackTime = Time.time + monstersConfig.SpiderInitialAttackDelay;
                return;
            }

            if (IsAttacking) return;

            JumpToPlayerFace();
            targetPlayer.SetDamageByMonsterRpc(AttackDamage);
            nextAttackTime = Time.time + monstersConfig.SpiderAttackInterval;
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetScreamRpc(bool scream)
        {
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                if (scream) monster.PlayAudio(data.ChaseSoundKey, true);
                else audioClient.Stop(data.ChaseSoundKey);
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetAttackAudioRpc()
        {
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
                monster.PlayAudio(data.AttackSoundKey, true);
        }
        
        [Rpc(RpcSources.All, RpcTargets.All)]
        private void SetNewTargetRpc(PlayerActor playerActor)
        {
            targetPlayer = playerActor;
            if (playersModel.LocalPlayer.Value == targetPlayer && !HasStateAuthority)
            {
                TransferAuthority().Forget();
            }
        }

        private void ReceiveDamage(Vector3 force, PlayerRef killer)
        {
            if (IsDamageReceiving) return;
            nextDamageReceiveTime = Time.time + 0.25f;

            var modifiedForce = force * 0.1f;
            if (modifiedForce.y < 0)
            {
                modifiedForce.y *= -0.1f;
            }

            rootRigidbody.AddForce(modifiedForce, ForceMode.Acceleration);
            if (playersModel.TryGetPlayer(killer.PlayerId, out var player))
            {
                SetNewTargetRpc(player);
            }
        }

        private bool UsingPhysics()
        {
            return !rootRigidbody.isKinematic && rootRigidbody.constraints != RigidbodyConstraints.FreezeAll;
        }

        private void MoveForward(float speed)
        {
            if (UsingPhysics()) return;
            MonsterTransform.position += speed * Time.fixedDeltaTime * Forward;
        }

        private bool TryRaycast(Vector3 point, Vector3 direction, float maxDistance, out RaycastHit hit)
        {
            return Physics.Raycast(point, direction, out hit, maxDistance, 1 << Layers.Default);
        }

        private void PlayRandomVoice()
        {
            if (Time.time < nextVoiceTime) return;

            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
                monster.PlayAudio(data.RandomSoundKey);

            nextVoiceTime = Time.time + Random.Range(5f, 15f);
        }

        private void HandleDamage(MonsterDamageArgs args)
        {
            if (!HasStateAuthority || args.monster != monster || !IsAlive) return;
            ReceiveDamage(args.direction, args.killer);
        }
    }
}