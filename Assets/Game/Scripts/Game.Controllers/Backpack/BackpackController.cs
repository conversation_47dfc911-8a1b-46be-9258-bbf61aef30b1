using System;
using System.Reactive;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.PlayerUI.Backpack;
using Modules.Core;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Backpack
{
    public class BackpackController : ControllerBase
    {
        private BackpackMenu backpackMenu;
        private PlayersModel playersModel;
        private CancellationTokenSource equipCancellationTokenSource;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool IsBackpackEquipped => LocalPlayer != null && LocalPlayer.Backpack != null;

        [Inject]
        private void Construct(PlayerMenu playerMenu, PlayersModel playersModel, IInteractableInteractionSubscriber interactableInteractionsSubscriber)
        {
            this.playersModel = playersModel;
            backpackMenu = playerMenu.BackpackMenu;

            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(DisposeCancellationToken);
            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleExitEntered).AddTo(DisposeCancellationToken);
            backpackMenu.OnUnequip.Subscribe(HandleUnequip).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            player.OnBeforeDeadLocal.Subscribe(HandleLocalPlayerBeforeDead).AddTo(player);
        }

        private void HandleLocalPlayerBeforeDead(Unit unit)
        {
            LocalPlayer.UnequipBackpack();
            backpackMenu.DeactivateMenu();
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (IsBackpackEquipped || args.interactable is not BackpackActor backpack)
            {
                return;
            }

            backpackMenu.RenderActivatingState(backpack.InteractableCode);
        }

        private void HandleExitEntered(InteractionArgs<SelectExitEventArgs> args)
        {
            if (IsBackpackEquipped || args.interactable is not BackpackActor backpack)
            {
                return;
            }

            if (backpackMenu.IsHoveredBackpack(backpack))
            {
                EquipBackpack(backpack).Forget();
            }
            else
            {
                backpackMenu.DeactivateMenu();
            }
        }

        private async UniTaskVoid EquipBackpack(BackpackActor backpack)
        {
            equipCancellationTokenSource.CancelAndDispose();

            if (LocalPlayer == null)
            {
                return;
            }

            equipCancellationTokenSource = new CancellationTokenSource();
            var token = CancellationTokenSource.CreateLinkedTokenSource(equipCancellationTokenSource.Token, backpack.destroyCancellationToken).Token;
            await UniTask.WaitWhile(() => backpack.IsGrabbed, cancellationToken: token);

            if (backpack == null)
            {
                return;
            }

            LocalPlayer.EquipBackpack(backpack);
            backpackMenu.RenderActivatedState(backpack.InteractableCode);
        }

        private void HandleUnequip(IXRSelectInteractor interactor)
        {
            if (LocalPlayer == null)
            {
                return;
            }

            var backpack = LocalPlayer.Backpack;
            LocalPlayer.UnequipBackpack();
            backpack.Grab(interactor);
        }
    }
}