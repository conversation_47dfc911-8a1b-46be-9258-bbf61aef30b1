using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.PlayerEffects
{
    public class PlayerEffectsManager : MonoBehaviour
    {
        [SerializeField] private SelfDamageEffectView selfDamageEffectViewPrefab;
        [SerializeField] private SelfDamageEffectView spiderMonsterDamageEffectViewPrefab;

        private IObjectResolver objectResolver;
        private SelfDamageEffectView selfDamageEffect;
        private SelfDamageEffectView spiderMonsterDamageEffect;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public void RenderSelfDamageEffect()
        {
            selfDamageEffect ??= objectResolver.Instantiate(selfDamageEffectViewPrefab, transform);
            selfDamageEffect.SetActive(true);
        }
        
        public void RenderSpiderBossDamageEffect(float duration = 2)
        {
            spiderMonsterDamageEffect ??= objectResolver.Instantiate(spiderMonsterDamageEffectViewPrefab, transform);
            spiderMonsterDamageEffect.Instantiate(duration);
        }
    }
}