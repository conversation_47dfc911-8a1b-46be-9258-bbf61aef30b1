using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using DG.Tweening;
using Modules.Core;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Views.PlayerEffects
{
    public class SelfDamageEffectView : Actor
    {
        [SerializeField] private Renderer selfRenderer;

        private Tweener effectTweener;
        private CancellationTokenSource effectCancellationTokenSource;

        private void OnDisable()
        {
            effectTweener?.Kill();
            effectCancellationTokenSource.CancelAndDispose();
        }

        public override void SetActive(bool isActive)
        {
            base.SetActive(isActive);
            if (isActive)
            {
                RenderEffect();
            }
        }

        public void Instantiate(float duration = 2, bool shake = false)
        {
            base.SetActive(true);
            RenderEffect(duration, shake);
        }

        private void RenderEffect(float duration = 2, bool shake = false)
        {
            effectTweener?.Kill();
            effectTweener = selfRenderer.sharedMaterial.DOFade(1, 0.1f);
            if (shake)
            {
                effectTweener = transform.DOShakePosition(0.5f, 0.5f);
                effectTweener = transform.DOShakeRotation(0.5f, 20f);
                effectTweener = transform.DOShakeScale(0.5f, 0.3f);
            }
            
            effectCancellationTokenSource.CancelAndDispose();
            effectCancellationTokenSource = new CancellationTokenSource();
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(1))
                .Subscribe(_ =>
                {
                    effectTweener?.Kill();
                    effectTweener = selfRenderer.sharedMaterial.DOFade(0, duration).OnComplete(Hide);
                })
                .AddTo(effectCancellationTokenSource.Token);
        }

        [Button]
        private void T()
        {
            SetActive(true);
        }
    }
}