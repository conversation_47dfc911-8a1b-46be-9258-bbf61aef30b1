using Fusion;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Views.Items
{
    public abstract class ItemActor : InteractableActor
    {
        [SerializeField] protected Transform viewParent;
        [SerializeField] private string impactAudioKey;

        private PlayersModel playersModel;
        private bool CanCollide => HasStateAuthority && !IsGrabbed && Rigidbody.velocity.sqrMagnitude > 0.01f;

        protected ItemsManager ItemsManager { get; private set; }
        protected ItemsConfig ItemsConfig { get; private set; }
        protected IAudioClient AudioClient { get; private set; }
        protected IXRInput XRInput { get; private set; }

        [Inject]
        private void Construct(
            IXRInput xrInput,
            ItemsConfig itemsConfig,
            IAudioClient audioClient,
            PlayersModel playersModel,
            ItemsManager itemsManager)
        {
            ItemsManager = itemsManager;
            ItemsConfig = itemsConfig;
            AudioClient = audioClient;
            XRInput = xrInput;
            this.playersModel = playersModel;
        }

        protected virtual void OnCollisionEnter(Collision collision)
        {
            if (!CanCollide)
            {
                return;
            }

            PlayImpactAudio();
        }

        protected virtual void PlayImpactAudio()
        {
            if (!HasStateAuthority || IsGrabbed)
            {
                return;
            }

            var playerList = playersModel.FindPlayers(transform.position);

            foreach (var player in playerList)
            {
                SendRpcSafe(() => PlayImpactAudioRpc(player.StateAuthority));
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void PlayImpactAudioRpc([RpcTarget] PlayerRef player)
        {
            var key = string.IsNullOrEmpty(impactAudioKey) ? AudioKeys.ImpactDefault : impactAudioKey;
            AudioClient.Play(key, transform.position, destroyCancellationToken);
        }

        public override void Spawned()
        {
            base.Spawned();
            InitializeView();
            ChangeGrabber();
            ChangeIsAttached();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            StopDropTimer();
            UninitializeView();
        }
    }

    public abstract class ItemActor<TView> : ItemActor where TView : ItemView
    {
        protected TView View { get; set; }
        protected bool HasView => View != null;

        protected override void ChangeGrabber()
        {
            base.ChangeGrabber();

            if (IsGrabbed)
            {
                if (HasView)
                {
                    View.SetGrabState(HasStateAuthority);
                }
            }
            else
            {
                if (HasView)
                {
                    View.SetDropState(true);
                }
            }

            UpdateDropTimer();
        }

        protected override void InitializeView()
        {
            base.InitializeView();

            if (ItemsManager.TryCreateView(InteractableId, viewParent, out var view) && view is TView currentView)
            {
                View = currentView;
            }
            else
            {
                ItemsManager.DestroyView(view);
            }

            InteractableCode = ItemsConfig.GetInteractableData(InteractableId)?.Code;
            InitializeGrabbing(View);
        }

        protected override void UninitializeView()
        {
            base.UninitializeView();
            UninitializeGrabbing();
            ItemsManager?.DestroyView(View);
            View = null;
        }

        protected virtual void UpdateDropTimer()
        {
            UpdateDropTimer(!IsGrabbed, DropTimeout);
        }
    }
}