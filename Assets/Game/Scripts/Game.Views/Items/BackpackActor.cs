using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.Guns;
using Game.Views.Interactables;
using Game.Views.InteractablesCore;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Items
{
    public class BackpackActor : ItemActor<BackpackView>
    {
        private const int MaxInventory = 12;
        private const float AddInventoryTimeout = 0.1f;
        private const float RemoveInventoryTimeOut = 0.5f;

        private float addInventoryTime;
        private float removeInventoryTime;
        private InteractablesManager interactablesManager;

        private bool HasAnyInventory => InventoryList.Count > 0;
        private bool IsFull => InventoryList.Count >= MaxInventory;
        private bool CanAddInventory => IsOpened && Time.time - addInventoryTime > AddInventoryTimeout && Time.time - removeInventoryTime > RemoveInventoryTimeOut;
        private bool CanRemoveInventory => HasAnyInventory && IsOpened && Time.time - removeInventoryTime > RemoveInventoryTimeOut;

        [Networked] [OnChangedRender(nameof(ChangeIsOpen))]
        public NetworkBool IsOpened { get; private set; }

        [Networked] [OnChangedRender(nameof(ChangeIsEquipped))]
        public NetworkBool IsEquipped { get; set; }

        [Networked] [Capacity(MaxInventory)] public NetworkLinkedList<BackpackInventory> InventoryList { get; } = default;

        [Inject]
        private void Construct(InteractablesManager interactablesManager)
        {
            this.interactablesManager = interactablesManager;
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            var isLeftHand = args.interactorObject.IsLeftHand();
            var onOpenClicked = isLeftHand ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onOpenClicked.Where(_ => HasStateAuthority && HasView).Subscribe(HandleOpenClicked).AddTo(DropCancellationToken);
        }

        public override void Spawned()
        {
            base.Spawned();
            ChangeIsEquipped();
            ChangeIsOpen();
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            if (IsGrabbed && HasAnyInventory && CanRemoveInventory)
            {
                var isLookDown = Vector3.Dot(transform.up, Vector3.down) > 0.8f;
                if (!isLookDown)
                {
                    return;
                }

                AudioClient.Play(AudioKeys.BackpackRemoveItem, transform.position, destroyCancellationToken);
                var backpackInventory = InventoryList.Get(InventoryList.Count - 1);
                InventoryList.Remove(backpackInventory);
                var interactable = interactablesManager.CreateActor(backpackInventory.interactableId, View.PocketPose);
                if (interactable is RaycastGunActor gun)
                {
                    gun.SetAmmo(backpackInventory.ammo);
                }

                removeInventoryTime = Time.time;
            }
        }

        protected override void UpdateDropTimer()
        {
            UpdateDropTimer(!IsGrabbed && !IsEquipped, DropTimeout);
        }

        protected override void OnCollisionEnter(Collision collision)
        {
            base.OnCollisionEnter(collision);
            if (HasStateAuthority
                && CanAddInventory
                && collision.gameObject.TryGetComponent(out InteractableActor interactable)
                && !interactable.IsGrabbed
                && interactable is not BackpackActor)
            {
                if (IsFull)
                {
                    AudioClient.Play(AudioKeys.BackpackFull, transform.position, destroyCancellationToken);
                }
                else
                {
                    AudioClient.Play(AudioKeys.BackpackAddItem, transform.position, destroyCancellationToken);
                    AddInventoryAsync(interactable).Forget();
                }
            }
        }

        protected override void PlayImpactAudio()
        {
            if (IsEquipped)
            {
                return;
            }

            base.PlayImpactAudio();
        }

        private void ChangeIsOpen()
        {
            if (HasView)
            {
                View.SetOpened(IsOpened, IsGrabbed);
            }
        }

        private void ChangeIsEquipped()
        {
            if (HasStateAuthority && IsEquipped)
            {
                IsOpened = false;
            }

            if (IsEquipped && HasView)
            {
                View.SetGrabState(false);
                SetActiveNetworkRigidbody(false);
            }
        }

        private void HandleOpenClicked(InputAction.CallbackContext obj)
        {
            if (obj.performed)
            {
                IsOpened = !IsOpened;
                AudioClient.Play(AudioKeys.BackpackOpenClose, transform.position, destroyCancellationToken);
            }
        }

        private async UniTaskVoid AddInventoryAsync(InteractableActor interactable)
        {
            if (!interactable.HasStateAuthority)
            {
                var result = await interactable.Object
                    .RequestStateAuthorityAsync(destroyCancellationToken)
                    .TimeoutWithoutException(TimeSpan.FromSeconds(1));

                if (result.IsTimeout)
                {
                    return;
                }
            }

            if (IsFull)
            {
                return;
            }

            var id = interactable.InteractableId;
            var ammo = interactable is RaycastGunActor gun ? gun.Ammo : (byte)0;

            InventoryList.Add(new BackpackInventory(id, ammo));
            Runner.Despawn(interactable.Object);
            addInventoryTime = Time.time;
        }

        public readonly struct BackpackInventory : INetworkStruct
        {
            public readonly byte interactableId;
            public readonly byte ammo;

            public BackpackInventory(byte interactableId, byte ammo)
            {
                this.interactableId = interactableId;
                this.ammo = ammo;
            }
        }
    }
}