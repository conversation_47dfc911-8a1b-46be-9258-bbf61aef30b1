using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Addons.Physics;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Grabbing
{
    [DefaultExecutionOrder(ExecutionOrder)]
    public abstract class GrabbableItem : NetworkActor, IStateAuthorityChanged
    {
        private const int ExecutionOrder = NetworkHand.ExecutionOrder + 10;
        private const float AttachThreshold = 0.01f;

        [Header("Grabbable Item")]
        [SerializeField] private XRSimpleGrabInteractable grabInteractable;
        [SerializeField] private NetworkRigidbody3D networkRigidbody;
        [SerializeField] private RigidbodySettings localGrabRigidbodySettings = new() { constraints = RigidbodyConstraints.FreezeAll };
        [SerializeField] private RigidbodySettings remoteGrabRigidbodySettings = new() { isKinematic = true };
        [SerializeField] private RigidbodySettings dropRigidbodySettings = new() { useGravity = true };

        private IPublisher<GrabbableItemGrabArgs> grabPublisher;
        private CancellationTokenSource grabLocalCancellationTokenSource;
        private CancellationTokenSource dropCancellationTokenSource;

        public int DropTimeout { get; set; } = 10;
        public Rigidbody Rigidbody => grabInteractable.Rigidbody;
        public NetworkRigidbody3D NetworkRigidbody => networkRigidbody;
        public NetworkHand Grabber { get; private set; }
        public bool IsGrabbed => Object != null && Grabber != null;
        public HandType HandType => IsGrabbed ? Grabber.HandType : HandType.Left;
        public bool IsLeftInteractor => SelectInteractor != null && SelectInteractor.IsLeftHand();
        public bool IsRightInteractor => SelectInteractor != null && SelectInteractor.IsRightHand();
        public IXRSelectInteractor SelectInteractor => grabInteractable.firstInteractorSelecting;
        public bool IsHovered => grabInteractable.isHovered;
        public float InitialAttachEaseInTime { get; private set; }
        public float InitialThrowVelocityScale { get; private set; }
        public float InitialThrowAngularVelocityScale { get; private set; }

        public CancellationToken DropCancellationToken
        {
            get
            {
                dropCancellationTokenSource ??= new CancellationTokenSource();
                return dropCancellationTokenSource.Token;
            }
        }

        public float AttachEaseInTime
        {
            get => grabInteractable.attachEaseInTime;
            set => grabInteractable.attachEaseInTime = value;
        }

        public float ThrowVelocityScale
        {
            get => grabInteractable.throwVelocityScale;
            set => grabInteractable.throwVelocityScale = value;
        }

        public float ThrowAngularVelocityScale
        {
            get => grabInteractable.throwAngularVelocityScale;
            set => grabInteractable.throwAngularVelocityScale = value;
        }

        public bool ThrowOnDetach
        {
            get => grabInteractable.throwOnDetach;
            set => grabInteractable.throwOnDetach = value;
        }

        [Networked] [OnChangedRender(nameof(ChangeGrabber))]
        public NetworkBehaviourId GrabberId { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeIsAttached))]
        public NetworkBool IsAttached { get; set; }

        [Networked] private TickTimer DropTimer { get; set; } = TickTimer.None;

        [Inject]
        private void Construct(IPublisher<GrabbableItemGrabArgs> grabPublisher)
        {
            this.grabPublisher = grabPublisher;
        }

        protected override void Awake()
        {
            base.Awake();
            InitialAttachEaseInTime = AttachEaseInTime;
            InitialThrowVelocityScale = ThrowVelocityScale;
            InitialThrowAngularVelocityScale = ThrowAngularVelocityScale;
        }

        public override void Spawned()
        {
            base.Spawned();
            grabInteractable.hoverEntered.AddListener(HandleHoverEntered);
            grabInteractable.hoverExited.AddListener(HandleHoverExited);
            grabInteractable.BeforeSelectEntering.AddListener(HandleBeforeSelectEntering);
            grabInteractable.SelectEntering.AddListener(HandleSelectEntering);
            grabInteractable.selectEntered.AddListener(HandleSelectEntered);
            grabInteractable.BeforeSelectExiting.AddListener(HandleBeforeSelectExiting);
            grabInteractable.SelectExiting.AddListener(HandleSelectExiting);
            grabInteractable.selectExited.AddListener(HandleSelectExited);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);

            Ungrab();

            dropCancellationTokenSource.CancelAndDispose();
            dropCancellationTokenSource = null;

            grabLocalCancellationTokenSource.CancelAndDispose();
            grabLocalCancellationTokenSource = null;

            grabInteractable.hoverEntered.RemoveListener(HandleHoverEntered);
            grabInteractable.hoverExited.RemoveListener(HandleHoverExited);
            grabInteractable.BeforeSelectEntering.RemoveListener(HandleBeforeSelectEntering);
            grabInteractable.SelectEntering.RemoveListener(HandleSelectEntering);
            grabInteractable.selectEntered.RemoveListener(HandleSelectEntered);
            grabInteractable.BeforeSelectExiting.RemoveListener(HandleBeforeSelectExiting);
            grabInteractable.SelectExiting.RemoveListener(HandleSelectExiting);
            grabInteractable.selectExited.RemoveListener(HandleSelectExited);
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();

            if (TryDespawnByPosition())
            {
                return;
            }

            TryStopDropTimerByExpiration();
        }

        public override void Render()
        {
            base.Render();
            UpdateLocalAttaching();
            UpdateRemotePositionAndRotation();
            TryRequestStateAuthorityIfNone();
        }

        public void Grab(IXRSelectInteractor interactor)
        {
            grabInteractable.Grab(interactor);
        }

        public void Ungrab()
        {
            grabInteractable.Ungrab();
        }

        private float lastRequestStateAuthorityTime;

        void IStateAuthorityChanged.StateAuthorityChanged()
        {
            if (HasStateAuthority && !grabInteractable.isSelected)
            {
                UpdateDropTimer(true, DropTimeout);
                DropLocal();
            }
        }

        protected virtual void HandleHoverEntered(HoverEnterEventArgs args)
        {
        }

        protected virtual void HandleHoverExited(HoverExitEventArgs args)
        {
        }

        protected virtual void HandleBeforeSelectEntering(SelectEnterEventArgs args)
        {
        }

        protected virtual void HandleSelectEntering(SelectEnterEventArgs args)
        {
        }

        protected virtual void HandleSelectEntered(SelectEnterEventArgs args)
        {
            GrabLocal(args.interactorObject.IsLeftHand()).Forget();
        }

        protected virtual void HandleBeforeSelectExiting(SelectExitEventArgs args)
        {
        }

        protected virtual void HandleSelectExiting(SelectExitEventArgs args)
        {
        }

        protected virtual void HandleSelectExited(SelectExitEventArgs args)
        {
            DropLocal();

            dropCancellationTokenSource.CancelAndDispose();
            dropCancellationTokenSource = null;
        }

        protected virtual void UpdateDropTimer(bool isStart, float dropTimeout)
        {
            if (HasStateAuthority)
            {
                DropTimer = isStart ? TickTimer.CreateFromSeconds(Runner, dropTimeout) : TickTimer.None;
            }
        }

        protected virtual void StopDropTimer()
        {
            UpdateDropTimer(false, 0);
        }

        protected virtual void ChangeGrabber()
        {
            SetupGrabber();

            if (HasStateAuthority)
            {
                if (IsGrabbed)
                {
                    ApplyLocalGrabRigidbodySettings();
                }
                else
                {
                    ApplyDropRigidbodySettings();
                    IsAttached = false;
                }
            }
            else
            {
                Ungrab();

                if (IsGrabbed)
                {
                    ApplyRemoteGrabRigidbodySettings();
                }
                else
                {
                    ApplyDropRigidbodySettings();
                }
            }
        }

        protected virtual void ChangeIsAttached()
        {
            SetActiveNetworkRigidbody(!IsAttached);
        }

        protected virtual void HandleDropTimerExpired()
        {
            Runner.Despawn(Object);
        }

        protected void SetLeftHandAttachNode(Transform node)
        {
            grabInteractable.LeftHandAttachNode = node;
        }

        protected void SetRightHandAttachNode(Transform node)
        {
            grabInteractable.RightHandAttachNode = node;
        }

        protected virtual void SetColliders(List<Collider> colliderList)
        {
            ClearColliders();
            grabInteractable.colliders.AddRange(colliderList);
        }

        protected virtual void ClearColliders()
        {
            grabInteractable.colliders.Clear();
        }

        protected void RegisterInteractable()
        {
            grabInteractable.RegisterInteractable();
        }

        protected void UnregisterInteractable()
        {
            grabInteractable.UnregisterInteractable();
        }

        protected void ApplyRigidbodySettings(RigidbodySettings settings)
        {
            Rigidbody.useGravity = settings.useGravity;
            Rigidbody.isKinematic = settings.isKinematic;
            Rigidbody.constraints = settings.constraints;
        }

        private bool TryStopDropTimerByExpiration()
        {
            if (DropTimer.Expired(Runner))
            {
                DropTimer = TickTimer.None;
                HandleDropTimerExpired();
                return true;
            }

            return false;
        }

        private bool TryDespawnByPosition()
        {
            if (!grabInteractable.isSelected && transform.position.y < -1)
            {
                Runner.Despawn(Object);
                return true;
            }

            return false;
        }

        private void SetupGrabber()
        {
            if (GrabberId == default)
            {
                Grabber = null;
            }
            else if (Runner.TryFindBehaviour(GrabberId, out var obj) && obj is NetworkHand networkHand)
            {
                Grabber = networkHand;
            }
        }

        private void ApplyLocalGrabRigidbodySettings()
        {
            ApplyRigidbodySettings(localGrabRigidbodySettings);
        }

        private void ApplyRemoteGrabRigidbodySettings()
        {
            ApplyRigidbodySettings(remoteGrabRigidbodySettings);
        }

        private void ApplyDropRigidbodySettings()
        {
            ApplyRigidbodySettings(dropRigidbodySettings);
        }

        private async UniTaskVoid GrabLocal(bool isLeftHand)
        {
            ApplyLocalGrabRigidbodySettings();

            if (!HasStateAuthority)
            {
                grabLocalCancellationTokenSource.CancelAndDispose();
                grabLocalCancellationTokenSource = new CancellationTokenSource();
                await Object.RequestStateAuthorityAsync(grabLocalCancellationTokenSource.Token);
            }

            if (HasStateAuthority)
            {
                grabPublisher.Publish(new GrabbableItemGrabArgs(this, isLeftHand));
            }
        }

        private void DropLocal()
        {
            ApplyDropRigidbodySettings();

            grabLocalCancellationTokenSource.CancelAndDispose();
            grabLocalCancellationTokenSource = null;

            if (HasStateAuthority)
            {
                GrabberId = default;
            }
        }

        protected void SetActiveNetworkRigidbody(bool isActive)
        {
            if (networkRigidbody.enabled == isActive)
            {
                return;
            }

            networkRigidbody.enabled = isActive;
        }

        private void UpdateLocalAttaching()
        {
            if (!HasStateAuthority || !IsGrabbed)
            {
                return;
            }

            if (!IsAttached && CanAttach())
            {
                IsAttached = true;
            }
        }

        private void UpdateRemotePositionAndRotation()
        {
            if (HasStateAuthority || !IsGrabbed || !IsAttached)
            {
                return;
            }

            var attachNode = GetAttachNode();

            if (attachNode == null)
            {
                return;
            }

            var rotation = Grabber.AttachNode.rotation * Quaternion.Inverse(attachNode.localRotation);
            var position = Grabber.AttachNode.position - rotation * transform.InverseTransformPoint(attachNode.position);
            transform.SetPositionAndRotation(position, rotation);
        }

        private bool CanAttach()
        {
            var attachNode = GetAttachNode();

            if (attachNode == null)
            {
                return false;
            }

            return (Grabber.AttachNode.position - attachNode.position).sqrMagnitude < AttachThreshold;
        }

        private Transform GetAttachNode()
        {
            return Grabber == null ? null : Grabber.HandType == HandType.Left ? grabInteractable.LeftHandAttachNode : grabInteractable.RightHandAttachNode;
        }

        private void TryRequestStateAuthorityIfNone()
        {
            if (Object.StateAuthority == PlayerRef.None && Runner.IsSharedModeMasterClient && Time.time - lastRequestStateAuthorityTime > 2)
            {
                Object.RequestStateAuthority();
                lastRequestStateAuthorityTime = Time.time;
            }
        }

        [Serializable]
        public class RigidbodySettings
        {
            public bool useGravity;
            public bool isKinematic;
            public RigidbodyConstraints constraints;
        }
    }
}