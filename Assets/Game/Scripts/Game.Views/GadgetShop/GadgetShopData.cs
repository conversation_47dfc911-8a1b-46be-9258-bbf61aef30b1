using System;
using Game.Core.Data;

namespace Game.Views.GadgetShop
{
    [Serializable]
    public class GadgetShopData
    {
        public string viewCode;
        public string title;
        public int diamondPrice;
        public int diamondReward;
        public InventoryCategory category;
        public bool disabledInShop;

        public override string ToString()
        {
            return title;
        }
    }
}